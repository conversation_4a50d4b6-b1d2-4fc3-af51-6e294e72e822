extends Node
class_name HandManager

signal hand_updated

const GameConfig = preload("res://Scripts/GameConfig.gd")

var player1_hand := []           # Main complète du joueur 1
var player2_hand := []           # Main complète du joueur 2
var player1_game_hand := []      # Main de jeu réduite du joueur 1 (après validation)
var player2_game_hand := []      # Main de jeu réduite du joueur 2 (après validation)
var selected_card_ids := []
var max_selected_cards := 5
var single_selection_mode := false  # Mode sélection unique après la phase initiale
var show_full_hand := false     # Afficher temporairement la main complète (pour échange)
var poker_hand_evaluator
var active_effects := {}
var time_elapsed := 0.0
var initial_positions := {}

func _ready() -> void:
	poker_hand_evaluator = load("res://Scripts/PokerHand.gd").new()
	add_child(poker_hand_evaluator)

func _process(delta: float) -> void:
	time_elapsed += delta

func initialize_solo_game() -> void:
	var initial_state := {
		"player1_hand": CardUtils.generate_random_hand(8),
		"player2_hand": CardUtils.generate_random_hand(8),
		"current_turn": 1,
		"game_state": "playing"
	}
	apply_game_state(initial_state)

func get_card_button(card_id: int) -> TextureButton:
	for child in get_parent().get_node("CardHand").get_children():
		if child.has_meta("card_id") and child.get_meta("card_id") == card_id:
			return child
	return null

# Get card information by ID
func get_card_info(card_id: int) -> Dictionary:
	return CardUtils.find_card_by_id(player1_hand, card_id)

# Get information for multiple cards by their IDs
func get_cards_info(card_ids: Array) -> Array:
	return CardUtils.get_cards_by_ids(player1_hand, card_ids)

# Evaluate a poker hand from card information
func evaluate_hand(cards_info: Array) -> String:
	var eval_cards = CardUtils.convert_cards_for_evaluation(cards_info)
	return poker_hand_evaluator.evaluate_hand(eval_cards)

func store_initial_position(card_button: Control) -> void:
	var card_id = card_button.get_meta("card_id")
	if not initial_positions.has(card_id):
		initial_positions[card_id] = card_button.position.y

func get_initial_position(card_id: int) -> float:
	return initial_positions.get(card_id, 0.0)

func cleanup_existing_effect(card_id: int) -> void:
	if active_effects.has(card_id):
		var effect = active_effects[card_id]
		if effect.has("tween") and is_instance_valid(effect.tween):
			effect.tween.kill()  # Arrêter le tween en cours
		active_effects.erase(card_id)

func select_card(card_id: int) -> void:
	var card_button = get_card_button(card_id)
	if not card_button or not is_instance_valid(card_button):
		return

	# If card is already selected, deselect it
	if selected_card_ids.has(card_id):
		selected_card_ids.erase(card_id)
		deselect_card(card_id)
		emit_signal("hand_updated")  # Ensure signal is emitted
		return

	# En mode sélection unique, désélectionner les autres cartes d'abord
	if single_selection_mode and selected_card_ids.size() > 0:
		clear_selection()

	# Check if we haven't exceeded the maximum number of cards
	var max_cards = 1 if single_selection_mode else max_selected_cards
	if selected_card_ids.size() >= max_cards:
		return

	# Add card to selected cards
	selected_card_ids.append(card_id)

	# Mark card as selected and apply effects
	card_button.set_meta("selected", true)

	# Activation animation
	store_initial_position(card_button)
	cleanup_existing_effect(card_id)

	# Create and bind tween to card node
	var tween = create_tween().bind_node(card_button)
	tween.set_parallel(true)

	# Upward movement animation
	var initial_y = get_initial_position(card_button.get_meta("card_id"))
	tween.tween_property(card_button, "position:y",
		initial_y - GameConfig.SELECTION_LIFT,
		GameConfig.ANIMATION_DURATION
	).set_trans(Tween.TRANS_BACK).set_ease(Tween.EASE_OUT)

	# Animation de mise à l'échelle
	tween.tween_property(card_button, "scale",
		GameConfig.SELECTION_SCALE,
		GameConfig.ANIMATION_DURATION
	).set_trans(Tween.TRANS_BACK).set_ease(Tween.EASE_OUT)

	# Animation de couleur
	tween.tween_property(card_button, "modulate",
		GameConfig.SELECTED_COLOR,
		GameConfig.ANIMATION_DURATION
	)

	active_effects[card_id] = {
		"type": "select",
		"start_time": time_elapsed,
		"tween": tween,
		"persist": true
	}

	emit_signal("hand_updated")  # S'assurer que le signal est émis

func deselect_card(card_id: int) -> void:
	var card_button = get_card_button(card_id)
	if not card_button or not is_instance_valid(card_button):
		return

	# Mark card as not selected
	card_button.set_meta("selected", false)

	# Deselection animation
	store_initial_position(card_button)
	cleanup_existing_effect(card_id)

	# Create and bind tween to card node
	var tween = create_tween().bind_node(card_button)
	tween.set_parallel(true)

	# Return to initial position
	var initial_y = get_initial_position(card_button.get_meta("card_id"))
	tween.tween_property(card_button, "position:y",
		initial_y,
		GameConfig.ANIMATION_DURATION
	).set_trans(Tween.TRANS_BACK).set_ease(Tween.EASE_IN)

	# Retour à l'échelle normale
	tween.tween_property(card_button, "scale",
		Vector2.ONE,
		GameConfig.ANIMATION_DURATION
	).set_trans(Tween.TRANS_BACK).set_ease(Tween.EASE_IN)

	# Retour à la couleur normale
	tween.tween_property(card_button, "modulate",
		GameConfig.NORMAL_COLOR,
		GameConfig.ANIMATION_DURATION
	)

	# Désactiver le glow si présent
	if card_button.has_node("Glow"):
		tween.tween_property(card_button.get_node("Glow"), "modulate:a",
			0.0, GameConfig.ANIMATION_DURATION
		)

	active_effects[card_id] = {
		"type": "deselect",
		"start_time": time_elapsed,
		"tween": tween,
		"persist": false
	}

func get_current_hand_state() -> Dictionary:
	# Retourner la main appropriée selon le contexte
	var display_hand = player1_hand
	if not show_full_hand and player1_game_hand.size() > 0:
		display_hand = player1_game_hand

	return {
		"player1_hand": display_hand,
		"player2_hand": player2_hand,
		"selected_cards": selected_card_ids,
		"single_selection_mode": single_selection_mode,
		"show_full_hand": show_full_hand
	}

func get_selected_cards() -> Array:
	return selected_card_ids

func clear_selection() -> void:
	# Deselect all currently selected cards
	for card_id in selected_card_ids.duplicate():
		deselect_card(card_id)

	# Clear the array
	selected_card_ids.clear()
	emit_signal("hand_updated")

func evaluate_selected_hand() -> String:
	if selected_card_ids.size() == 0:
		return "No cards selected"

	# Get card information for selected cards
	var selected_cards_info = get_cards_info(selected_card_ids)
	return evaluate_hand(selected_cards_info)

func select_best_hand() -> void:
	# Get all possible combinations of cards
	var _best_hand_value = ""
	var best_hand_cards = []
	var best_hand_score = -1

	# Create a list of all card objects
	var card_objects = []
	for card in player1_hand:
		card_objects.append({
			"id": card.id,
			"rank": card.value,
			"suit": card.suit
		})

	# Check all possible combinations
	var combinations = get_all_combinations(card_objects)

	for combo in combinations:
		# Convert to the format expected by the evaluator
		var eval_cards = []
		for card in combo:
			eval_cards.append({
				"rank": card.rank,
				"suit": card.suit
			})

		# Evaluate this combination
		var hand_value = poker_hand_evaluator.evaluate_hand(eval_cards)
		var hand_score = get_hand_score(hand_value)

		# If this is better than our current best, update
		if hand_score > best_hand_score:
			best_hand_score = hand_score
			_best_hand_value = hand_value
			best_hand_cards = combo

	# Select only the cards needed for the best hand
	for card in best_hand_cards:
		select_card(card.id)

	# Update the UI
	emit_signal("hand_updated")

# Helper function to get all possible combinations of cards
func get_all_combinations(cards: Array) -> Array:
	var result = []

	# Check all possible combinations from 2 to 5 cards
	for size in range(2, min(6, cards.size() + 1)):
		result.append_array(get_combinations(cards, size))

	return result

# Generate combinations of a specific size
func get_combinations(cards: Array, size: int) -> Array:
	var result = []

	if size == 0:
		return [[]]

	if cards.size() == 0:
		return []

	var first = cards[0]
	var rest = cards.slice(1)

	# Combinations that include the first element
	var combos_with_first = get_combinations(rest, size - 1)
	for combo in combos_with_first:
		combo.push_front(first)
		result.append(combo)

	# Combinations that exclude the first element
	var combos_without_first = get_combinations(rest, size)
	result.append_array(combos_without_first)

	return result

# Score different poker hands for comparison
func get_hand_score(hand_value: String) -> int:
	if hand_value.begins_with("Straight Flush"):
		return 9
	elif hand_value.begins_with("Four of a Kind"):
		return 8
	elif hand_value.begins_with("Full House"):
		return 7
	elif hand_value.begins_with("Flush"):
		return 6
	elif hand_value.begins_with("Straight"):
		return 5
	elif hand_value.begins_with("Three of a Kind"):
		return 4
	elif hand_value.begins_with("Two Pair"):
		return 3
	elif hand_value.begins_with("Pair"):
		return 2
	elif hand_value.begins_with("High Card"):
		return 1
	else:
		return 0

# Modify a card's rank
func modify_card_rank(card_id: int, change: int) -> void:
	# Find the card in the player's hand
	for i in range(player1_hand.size()):
		if player1_hand[i].id == card_id:
			# Modify the rank, ensuring it stays within valid range (1-13)
			var new_rank = player1_hand[i].value + change
			new_rank = clamp(new_rank, 1, 13)
			player1_hand[i].value = new_rank
			# Marquer la carte comme modifiée
			player1_hand[i].modified = true
			break

	# Update the UI
	emit_signal("hand_updated")

# Modify a card's suit
func modify_card_suit(card_id: int, new_suit: String) -> void:
	# Find the card in the player's hand
	for i in range(player1_hand.size()):
		if player1_hand[i].id == card_id:
			# Change the suit
			player1_hand[i].suit = new_suit
			# Marquer la carte comme modifiée
			player1_hand[i].modified = true
			break

	# Update the UI
	emit_signal("hand_updated")

# Valide la sélection et crée la main de jeu réduite
func validate_card_selection(selected_cards: Array) -> void:
	# Créer la main de jeu avec seulement les cartes sélectionnées
	player1_game_hand = []
	for card_id in selected_cards:
		var card = CardUtils.find_card_by_id(player1_hand, card_id)
		if not card.is_empty():
			player1_game_hand.append(card)

	# Passer en mode sélection unique
	single_selection_mode = true
	show_full_hand = false

	# Désélectionner toutes les cartes
	clear_selection()

	print("Main de jeu validée avec ", player1_game_hand.size(), " cartes")
	emit_signal("hand_updated")

# Active temporairement l'affichage de la main complète pour l'échange
func show_full_hand_for_exchange() -> void:
	show_full_hand = true
	emit_signal("hand_updated")

# Retourne à l'affichage de la main de jeu réduite
func hide_full_hand_after_exchange() -> void:
	show_full_hand = false
	clear_selection()
	emit_signal("hand_updated")

# Réinitialise pour un nouveau round
func reset_for_new_round() -> void:
	player1_game_hand = []
	player2_game_hand = []
	single_selection_mode = false
	show_full_hand = false
	clear_selection()

func apply_game_state(state: Dictionary) -> void:
	player1_hand = state.player1_hand
	player2_hand = state.player2_hand
	emit_signal("hand_updated")
