extends Control

@onready var hand_manager: HandManager = $HandManager
@onready var ui_manager: CardGameUI = $UIManager
@onready var multiplayer_manager: MultiplayerManager = $MultiplayerManager
@onready var effect_manager: EffectManager = $EffectManager

# Buttons
@onready var menu_button: Button = $MenuButton
@onready var best_hand_button: Button = $ButtonsContainer/BestHandButton
@onready var cheat_rank_button: Button = $ButtonsContainer/CheatRankButton
@onready var cheat_color_button: Button = $ButtonsContainer/CheatColorButton
@onready var play_hand_button: Button = $ButtonsContainer/PlayHandButton

# Panels
@onready var change_rank_panel: Panel = $ChangeRankPanel
@onready var change_color_panel: Panel = $ChangeColorPanel
@onready var winner_screen: Panel = $WinnerScreen

# Variables d'état du jeu
var is_multiplayer := false
var game_state := "setup"  # setup, card_selection, action_phase, final_validation, round_end, game_end
var game_phase := "card_selection"  # card_selection, action_phase, final_validation, reveal
var current_turn := 1
var selected_card_for_cheat = null

# Variables pour les phases de jeu
var active_player := 1  # 1 or 2
var first_player := 1   # Joueur qui commence le round (déterminé par pile ou face)

# Phase de sélection des cartes
var player1_cards_selected := false  # Le joueur 1 a-t-il validé sa sélection ?
var player2_cards_selected := false  # Le joueur 2 a-t-il validé sa sélection ?
var player1_selected_hand := []      # Cartes sélectionnées par le joueur 1
var player2_selected_hand := []      # Cartes sélectionnées par le joueur 2

# Phase d'action (échange/modification)
var player1_action_taken := false    # Le joueur 1 a-t-il fait son action ?
var player2_action_taken := false    # Le joueur 2 a-t-il fait son action ?
var action_count := 0                # Nombre d'actions effectuées ce round

# Phase de validation finale
var player1_final_validated := false # Le joueur 1 a-t-il validé sa main finale ?
var player2_final_validated := false # Le joueur 2 a-t-il validé sa main finale ?
var player1_final_hand := []         # Main finale du joueur 1
var player2_final_hand := []         # Main finale du joueur 2

# Pouvoirs de triche (une seule utilisation par round)
var cheat_rank_used := false
var cheat_color_used := false
var exchange_used := false

# Score et rounds
var current_round := 1
var max_rounds := 3
var player1_score := 0
var player2_score := 0

func _ready() -> void:
	hand_manager.connect("hand_updated", _on_hand_updated)
	multiplayer_manager.connect("game_state_updated", _on_game_state_updated)

	# S'assurer que les gestionnaires sont bien initialisés
	if not hand_manager or not ui_manager or not effect_manager:
		push_error("Required managers not found")
		return

	# Connect button signals
	menu_button.pressed.connect(_on_menu_button_pressed)
	best_hand_button.pressed.connect(_on_best_hand_button_pressed)
	cheat_rank_button.pressed.connect(_on_cheat_rank_button_pressed)
	cheat_color_button.pressed.connect(_on_cheat_color_button_pressed)
	play_hand_button.pressed.connect(_on_play_hand_button_pressed)

	# Les anciens boutons ont été supprimés, nous utilisons maintenant des cartes visuelles

	# Hide panels initially
	change_rank_panel.visible = false
	change_color_panel.visible = false
	winner_screen.visible = false

	# Configurer la détection du clic droit global
	set_process_input(true)

	initialize_game()

func initialize_game() -> void:
	if is_multiplayer:
		multiplayer_manager.initialize_multiplayer_game()
	else:
		hand_manager.initialize_solo_game()

	# Commencer par la phase de sélection des cartes
	game_state = "playing"
	game_phase = "card_selection"

	# Réinitialiser toutes les variables de round
	reset_round_variables()

	# Update interface to show current phase
	update_game_ui()

func _on_hand_updated() -> void:
	var current_state = hand_manager.get_current_hand_state()
	ui_manager.update_ui(current_state)
	effect_manager.update_effects()

func _on_game_state_updated(state: Dictionary) -> void:
	hand_manager.apply_game_state(state)
	ui_manager.update_ui(hand_manager.get_current_hand_state())

func _input(event: InputEvent) -> void:
	# Detect right click anywhere in the scene
	if event is InputEventMouseButton and event.button_index == 2 and event.pressed: # 2 = right mouse button
		# Deselect all cards
		hand_manager.clear_selection()

func _on_card_selected(card_id: int) -> void:
	hand_manager.select_card(card_id)
	effect_manager.play_card_selection_effect(card_id)

# Button handlers
func _on_menu_button_pressed() -> void:
	# Return to main menu
	get_tree().change_scene_to_file("res://Scenes/MainMenu.tscn")

# Find and select the best possible hand
func _on_best_hand_button_pressed() -> void:
	# Seulement disponible pendant la phase de sélection
	if game_phase != "card_selection":
		return

	# Clear current selection
	hand_manager.clear_selection()
	# Find and select the best hand
	hand_manager.select_best_hand()

# Bouton pour échanger une carte (action légale)
func _on_cheat_rank_button_pressed() -> void:
	# Renommer ce bouton en "Échanger carte" dans l'interface
	if game_phase != "action_phase":
		print("Vous ne pouvez échanger qu'en phase d'action")
		return

	if active_player != 1:
		print("Ce n'est pas votre tour")
		return

	if player1_action_taken or exchange_used:
		print("Vous avez déjà fait votre action ce round")
		return

	# Afficher temporairement la main complète pour l'échange
	hand_manager.show_full_hand_for_exchange()

	# Attendre que le joueur sélectionne une carte
	print("Sélectionnez une carte de votre main complète à échanger")

	# Le processus continue dans _on_exchange_card_selected()

# Bouton pour modifier une carte (action illégale)
func _on_cheat_color_button_pressed() -> void:
	# Renommer ce bouton en "Modifier carte" dans l'interface
	if game_phase != "action_phase":
		print("Vous ne pouvez modifier qu'en phase d'action")
		return

	if active_player != 1:
		print("Ce n'est pas votre tour")
		return

	if player1_action_taken or (cheat_rank_used and cheat_color_used):
		print("Vous avez déjà fait votre action ce round")
		return

	# Get the currently selected card from the game hand
	var selected_cards = hand_manager.get_selected_cards()
	if selected_cards.size() != 1:
		print("Vous devez sélectionner exactement une carte de votre main de jeu à modifier")
		return

	selected_card_for_cheat = selected_cards[0]

	# Créer une visualisation des options de modification (rang ou couleur)
	create_modification_options_visualization(selected_card_for_cheat)

	change_color_panel.visible = true
	change_rank_panel.visible = false

# Nouveau système de bouton "Valider" qui s'adapte à la phase actuelle
func _on_play_hand_button_pressed() -> void:
	var selected_cards = hand_manager.get_selected_cards()

	match game_phase:
		"card_selection":
			_handle_card_selection_validation(selected_cards)
		"action_phase":
			if hand_manager.show_full_hand:
				# Mode échange : confirmer l'échange
				_on_exchange_card_selected()
			else:
				# Mode normal : passer son tour
				_handle_skip_action()
		"final_validation":
			_handle_final_validation(selected_cards)

# Gère la validation de la sélection de cartes
func _handle_card_selection_validation(selected_cards: Array) -> void:
	if selected_cards.size() == 0:
		print("Vous devez sélectionner au moins une carte")
		return

	# Valider la sélection pour le joueur 1 (humain)
	if not player1_cards_selected:
		player1_selected_hand = selected_cards.duplicate()
		player1_cards_selected = true

		# Valider la sélection dans le HandManager (crée la main de jeu réduite)
		hand_manager.validate_card_selection(selected_cards)

		print("Joueur 1 a validé sa sélection de cartes")

		# L'IA valide automatiquement sa sélection
		ai_select_cards()

		# Passer à la phase d'action après pile ou face
		start_action_phase()

# Gère le fait de passer son tour d'action
func _handle_skip_action() -> void:
	if active_player == 1 and not player1_action_taken:
		player1_action_taken = true
		print("Joueur 1 passe son tour d'action")
	elif active_player == 2 and not player2_action_taken:
		player2_action_taken = true
		print("Joueur 2 passe son tour d'action")

	# Vérifier si on peut passer à la validation finale
	check_action_phase_complete()

# Gère la validation finale de la main
func _handle_final_validation(selected_cards: Array) -> void:
	# En phase de validation finale, on valide toute la main de jeu
	if not player1_final_validated:
		# Si aucune carte n'est sélectionnée, valider toute la main de jeu
		if selected_cards.size() == 0:
			player1_final_hand = []
			for card in hand_manager.player1_game_hand:
				player1_final_hand.append(card.id)
		else:
			player1_final_hand = selected_cards.duplicate()

		player1_final_validated = true
		print("Joueur 1 a validé sa main finale")

		# L'IA valide automatiquement sa main finale
		ai_validate_final_hand()

		# Révéler les mains et déterminer le gagnant
		reveal_hands_and_determine_winner()

# Gère la sélection de carte pour l'échange
func _on_exchange_card_selected() -> void:
	var selected_cards = hand_manager.get_selected_cards()
	if selected_cards.size() != 1:
		print("Vous devez sélectionner exactement une carte à échanger")
		return

	# Effectuer l'échange légal
	exchange_card_legal(selected_cards[0])

	# Retourner à l'affichage de la main de jeu
	hand_manager.hide_full_hand_after_exchange()

	# Marquer l'action comme effectuée
	player1_action_taken = true
	exchange_used = true

	print("Carte échangée avec succès")

	# Vérifier si on peut passer à la phase suivante
	check_action_phase_complete()



# Gestionnaire pour la modification de rang
func _on_rank_modification_selected(card_id: int, new_rank: int) -> void:
	print("Rank modification selected: ", card_id, " new rank: ", new_rank)

	# Modifier le rang de la carte
	var change = new_rank - hand_manager.get_card_info(card_id).value
	hand_manager.modify_card_rank(card_id, change)

	# Cacher le panel et marquer l'action comme effectuée
	change_color_panel.visible = false
	cheat_rank_used = true
	player1_action_taken = true
	action_count += 1
	selected_card_for_cheat = null

	print("Joueur 1 a modifié le rang d'une carte (illégal)")
	check_action_phase_complete()

# Gestionnaire pour la modification de couleur
func _on_suit_modification_selected(card_id: int, new_suit: String) -> void:
	print("Suit modification selected: ", card_id, " new suit: ", new_suit)

	# Modifier la couleur de la carte
	hand_manager.modify_card_suit(card_id, new_suit)

	# Cacher le panel et marquer l'action comme effectuée
	change_color_panel.visible = false
	cheat_color_used = true
	player1_action_taken = true
	action_count += 1
	selected_card_for_cheat = null

	print("Joueur 1 a modifié la couleur d'une carte (illégal)")
	check_action_phase_complete()

# Les anciennes fonctions de gestion des boutons ont été supprimées
# Nous utilisons maintenant _on_rank_option_selected et _on_suit_option_selected

# Réinitialise toutes les variables pour un nouveau round
func reset_round_variables() -> void:
	player1_cards_selected = false
	player2_cards_selected = false
	player1_selected_hand = []
	player2_selected_hand = []
	player1_action_taken = false
	player2_action_taken = false
	action_count = 0
	player1_final_validated = false
	player2_final_validated = false
	player1_final_hand = []
	player2_final_hand = []
	cheat_rank_used = false
	cheat_color_used = false
	exchange_used = false
	active_player = 1
	first_player = 1

	# Réinitialiser le HandManager
	hand_manager.reset_for_new_round()

# L'IA sélectionne automatiquement ses cartes
func ai_select_cards() -> void:
	# L'IA sélectionne sa meilleure main possible
	var best_hand = ai_select_best_hand()
	player2_selected_hand = best_hand
	player2_cards_selected = true
	print("L'IA a sélectionné ses cartes")

# Détermine le premier joueur aléatoirement (pile ou face) et commence la phase d'action
func start_action_phase() -> void:
	# Pile ou face pour déterminer qui commence
	if randf() < 0.5:
		first_player = 1
		active_player = 1
		print("Pile ou face : Le joueur 1 commence")
	else:
		first_player = 2
		active_player = 2
		print("Pile ou face : Le joueur 2 commence")

	# Passer à la phase d'action
	game_phase = "action_phase"
	update_game_ui()

	# Si l'IA commence, la faire jouer
	if active_player == 2:
		ai_play_action_turn()

# Effectue un échange légal de carte
func exchange_card_legal(card_id: int) -> void:
	# Trouver la carte dans la main du joueur
	var card_index = -1
	for i in range(hand_manager.player1_hand.size()):
		if hand_manager.player1_hand[i].id == card_id:
			card_index = i
			break

	if card_index == -1:
		print("Carte non trouvée")
		return

	# Générer une nouvelle carte aléatoire pour remplacer
	var new_card = CardUtils.create_random_card()
	hand_manager.player1_hand[card_index] = new_card

	print("Carte échangée légalement")
	hand_manager.emit_signal("hand_updated")

# Vérifie si la phase d'action est terminée
func check_action_phase_complete() -> void:
	# Chaque joueur peut faire maximum une action par round
	# Si les deux ont fait leur action OU si un joueur passe, on passe à la validation finale
	if (player1_action_taken and player2_action_taken) or action_count >= 2:
		start_final_validation_phase()
	else:
		# Passer au joueur suivant
		switch_active_player()

# Change le joueur actif
func switch_active_player() -> void:
	active_player = 3 - active_player  # 1 -> 2, 2 -> 1
	update_game_ui()

	# Si c'est le tour de l'IA, la faire jouer
	if active_player == 2:
		ai_play_action_turn()

# Commence la phase de validation finale
func start_final_validation_phase() -> void:
	game_phase = "final_validation"
	active_player = 1  # Le joueur humain valide en premier
	print("Phase de validation finale - Sélectionnez votre main finale")
	update_game_ui()

# L'IA valide sa main finale
func ai_validate_final_hand() -> void:
	# L'IA sélectionne sa meilleure main possible avec ses cartes actuelles
	var best_hand = ai_select_best_hand()
	player2_final_hand = best_hand
	player2_final_validated = true
	print("L'IA a validé sa main finale")

# Met à jour l'interface utilisateur en fonction de l'état du jeu
func update_game_ui() -> void:
	var hand_value_label = get_node("HandValue")

	# Mettre à jour le texte selon la phase
	match game_phase:
		"card_selection":
			hand_value_label.text = "Sélectionnez vos cartes et validez - Round " + str(current_round) + "/" + str(max_rounds) + " - Score: " + str(player1_score) + "-" + str(player2_score)
			# Boutons disponibles : sélection + validation
			best_hand_button.disabled = false
			cheat_rank_button.disabled = true  # "Échanger" - pas disponible en sélection
			cheat_color_button.disabled = true # "Modifier" - pas disponible en sélection
			play_hand_button.disabled = false  # "Valider sélection"

		"action_phase":
			if active_player == 1:
				if hand_manager.show_full_hand:
					hand_value_label.text = "Sélectionnez une carte à échanger puis cliquez 'Confirmer échange'"
					cheat_rank_button.disabled = true
					cheat_color_button.disabled = true
					play_hand_button.disabled = false  # "Confirmer échange"
				else:
					hand_value_label.text = "Votre tour d'action - Échangez, modifiez ou passez"
					cheat_rank_button.disabled = player1_action_taken or exchange_used
					cheat_color_button.disabled = player1_action_taken or (cheat_rank_used and cheat_color_used)
					play_hand_button.disabled = false  # "Passer"
			else:
				hand_value_label.text = "Tour de l'IA..."
				cheat_rank_button.disabled = true
				cheat_color_button.disabled = true
				play_hand_button.disabled = true
			best_hand_button.disabled = true

		"final_validation":
			hand_value_label.text = "Validez votre main finale (toutes les cartes de votre main de jeu)"
			best_hand_button.disabled = true  # Pas besoin de sélectionner, on valide tout
			cheat_rank_button.disabled = true
			cheat_color_button.disabled = true
			play_hand_button.disabled = false  # "Valider main finale"

# L'IA joue son tour d'action
func ai_play_action_turn() -> void:
	if player2_action_taken:
		return

	# Créer un timer pour simuler la réflexion de l'IA
	var timer = get_tree().create_timer(1.5)
	timer.timeout.connect(func(): _ai_execute_action())

func _ai_execute_action() -> void:
	# L'IA décide si elle veut faire une action (40% de chance)
	if randf() < 0.4:
		# Décider entre échange légal (60%) et modification illégale (40%)
		if randf() < 0.6:
			ai_exchange_card_legal()
		else:
			ai_modify_card_illegal()
	else:
		# L'IA passe son tour
		print("L'IA passe son tour d'action")

	player2_action_taken = true
	action_count += 1
	check_action_phase_complete()

# L'IA effectue un échange légal
func ai_exchange_card_legal() -> void:
	if hand_manager.player2_hand.size() > 0:
		var random_index = randi() % hand_manager.player2_hand.size()
		var new_card = CardUtils.create_random_card()
		hand_manager.player2_hand[random_index] = new_card
		print("L'IA a échangé une carte légalement")
		hand_manager.emit_signal("hand_updated")

# L'IA effectue une modification illégale
func ai_modify_card_illegal() -> void:
	if hand_manager.player2_hand.size() > 0:
		var random_index = randi() % hand_manager.player2_hand.size()
		var card = hand_manager.player2_hand[random_index]

		# Décider entre modification de rang ou de couleur
		if randf() < 0.5:
			# Modifier le rang
			var change = [-1, 1][randi() % 2]
			var new_rank = clamp(card.value + change, 1, 13)
			card.value = new_rank
			card.modified = true
			print("L'IA a modifié le rang d'une carte (illégal)")
		else:
			# Modifier la couleur
			var suits = ["hearts", "diamonds", "clubs", "spades"]
			suits.erase(card.suit)
			card.suit = suits[randi() % suits.size()]
			card.modified = true
			print("L'IA a modifié la couleur d'une carte (illégal)")

		hand_manager.emit_signal("hand_updated")

# Révèle les mains et détermine le gagnant
func reveal_hands_and_determine_winner() -> void:
	game_phase = "reveal"

	# Évaluer les mains finales
	var player1_hand_info = hand_manager.get_cards_info(player1_final_hand)
	var player2_hand_info = hand_manager.get_cards_info(player2_final_hand)

	# Vérifier si des cartes ont été modifiées (illégalement)
	var player1_has_cheated = has_modified_cards(player1_hand_info)
	var player2_has_cheated = has_modified_cards(player2_hand_info)

	# Évaluer les mains
	var player1_hand_value = hand_manager.evaluate_hand(player1_hand_info)
	var player2_hand_value = hand_manager.evaluate_hand(player2_hand_info)
	var player1_hand_score = hand_manager.get_hand_score(player1_hand_value)
	var player2_hand_score = hand_manager.get_hand_score(player2_hand_value)

	# Déterminer le gagnant selon les règles
	var winner = 0
	var result_text = ""

	# Règle spéciale: si un seul joueur a triché, l'autre gagne
	if player1_has_cheated and not player2_has_cheated:
		winner = 2
		result_text = "Le joueur 2 gagne car le joueur 1 a triché !"
	elif player2_has_cheated and not player1_has_cheated:
		winner = 1
		result_text = "Le joueur 1 gagne car le joueur 2 a triché !"
	else:
		# Les deux ont triché ou aucun n'a triché, on compare les mains
		if player1_hand_score > player2_hand_score:
			winner = 1
			result_text = "Le joueur 1 gagne avec " + player1_hand_value + " contre " + player2_hand_value
		elif player2_hand_score > player1_hand_score:
			winner = 2
			result_text = "Le joueur 2 gagne avec " + player2_hand_value + " contre " + player1_hand_value
		else:
			result_text = "Égalité ! " + player1_hand_value

	# Mettre à jour le score
	if winner == 1:
		player1_score += 1
	elif winner == 2:
		player2_score += 1

	# Afficher le résultat
	show_round_result(result_text)

# Affiche le résultat du round
func show_round_result(result_text: String) -> void:
	game_state = "round_end"

	# Afficher le résultat
	winner_screen.visible = true
	winner_screen.get_node("Label").text = result_text + "\n\nScore: " + str(player1_score) + "-" + str(player2_score)

	# Préparer le prochain round
	current_round += 1
	if current_round > max_rounds or player1_score > max_rounds/2 or player2_score > max_rounds/2:
		# Fin de la partie
		game_state = "game_end"
		var final_result = ""
		if player1_score > player2_score:
			final_result = "Le joueur 1 remporte la partie !"
		elif player2_score > player1_score:
			final_result = "Le joueur 2 remporte la partie !"
		else:
			final_result = "Égalité !"
		winner_screen.get_node("Label").text += "\n\n" + final_result
		# Ajouter un bouton pour recommencer
		var restart_button = Button.new()
		restart_button.text = "Nouvelle partie"
		restart_button.position = Vector2(350, 500)
		restart_button.size = Vector2(200, 50)
		restart_button.pressed.connect(func(): get_tree().reload_current_scene())
		winner_screen.add_child(restart_button)
	else:
		# Préparer le prochain round
		var continue_button = Button.new()
		continue_button.text = "Round suivant"
		continue_button.position = Vector2(350, 500)
		continue_button.size = Vector2(200, 50)
		continue_button.pressed.connect(start_next_round)
		winner_screen.add_child(continue_button)

# Crée une visualisation des options de modification (rang et couleur)
func create_modification_options_visualization(card_id: int) -> void:
	print("Creating modification options for card ID: ", card_id)
	# Nettoyer le panel existant
	for child in change_color_panel.get_children():
		if child is Label:
			continue # Garder seulement le label
		child.queue_free()

	# Obtenir les informations de la carte
	var card_info = hand_manager.get_card_info(card_id)
	if not card_info:
		return

	# Créer un conteneur principal
	var main_container = VBoxContainer.new()
	main_container.alignment = BoxContainer.ALIGNMENT_CENTER
	main_container.size_flags_horizontal = Control.SIZE_FILL
	main_container.size_flags_vertical = Control.SIZE_FILL
	main_container.position.y = 60
	change_color_panel.add_child(main_container)

	# Créer des boutons pour modifier le rang
	var rank_container = HBoxContainer.new()
	rank_container.alignment = BoxContainer.ALIGNMENT_CENTER
	main_container.add_child(rank_container)

	var rank_label = Label.new()
	rank_label.text = "Modifier le rang:"
	main_container.add_child(rank_label)

	# Options de rang (rang-1 et rang+1)
	if card_info.value > 1:
		var rank_down_button = Button.new()
		rank_down_button.text = "Rang -1"
		rank_down_button.pressed.connect(func(): _on_rank_modification_selected(card_id, card_info.value - 1))
		rank_container.add_child(rank_down_button)

	if card_info.value < 13:
		var rank_up_button = Button.new()
		rank_up_button.text = "Rang +1"
		rank_up_button.pressed.connect(func(): _on_rank_modification_selected(card_id, card_info.value + 1))
		rank_container.add_child(rank_up_button)

	# Créer des boutons pour modifier la couleur
	var suit_label = Label.new()
	suit_label.text = "Modifier la couleur:"
	main_container.add_child(suit_label)

	var suit_container = HBoxContainer.new()
	suit_container.alignment = BoxContainer.ALIGNMENT_CENTER
	main_container.add_child(suit_container)

	var suits = ["hearts", "diamonds", "clubs", "spades"]
	for suit in suits:
		if suit != card_info.suit:
			var suit_button = Button.new()
			suit_button.text = suit.capitalize()
			suit_button.pressed.connect(func(): _on_suit_modification_selected(card_id, suit))
			suit_container.add_child(suit_button)

# Vérifie si une main contient des cartes modifiées
func has_modified_cards(cards_info: Array) -> bool:
	for card in cards_info:
		if card.has("modified") and card.modified:
			return true
	return false



# Sélectionne la meilleure main possible pour l'IA
func ai_select_best_hand() -> Array:
	# Utiliser l'algorithme de sélection de la meilleure main
	var best_hand_ids = []
	var best_hand_score = -1
	var best_hand_cards = []

	# Créer une liste de toutes les cartes du joueur 2
	var card_objects = []
	for card in hand_manager.player2_hand:
		card_objects.append({
			"id": card.id,
			"rank": card.value,
			"suit": card.suit,
			"modified": card.modified
		})

	# Check all possible combinations
	var combinations = hand_manager.get_all_combinations(card_objects)

	for combo in combinations:
		# Convert to format expected by evaluator
		var eval_cards = []
		for card in combo:
			eval_cards.append({
				"rank": card.rank,
				"suit": card.suit
			})

		# Evaluate this combination
		var hand_value = hand_manager.poker_hand_evaluator.evaluate_hand(eval_cards)
		var hand_score = hand_manager.get_hand_score(hand_value)

		# If this is better than our current best hand, update
		if hand_score > best_hand_score:
			best_hand_score = hand_score
			best_hand_cards = combo

	# Extract IDs of cards from the best hand
	for card in best_hand_cards:
		best_hand_ids.append(card.id)

	return best_hand_ids



# Démarre le round suivant
func start_next_round() -> void:
	# Cacher l'écran de résultat
	winner_screen.visible = false

	# Supprimer les boutons ajoutés
	for child in winner_screen.get_children():
		if child is Button:
			child.queue_free()

	# Désélectionner toutes les cartes
	hand_manager.clear_selection()

	# Distribuer de nouvelles cartes
	hand_manager.initialize_solo_game()

	# Réinitialiser les variables pour le nouveau round
	reset_round_variables()

	# Commencer par la phase de sélection
	game_state = "playing"
	game_phase = "card_selection"

	# Mettre à jour l'interface
	update_game_ui()
