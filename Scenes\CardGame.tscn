[gd_scene load_steps=8 format=3 uid="uid://c2wlh8lw4ldt8"]

[ext_resource type="Script" uid="uid://booced0hj67a6" path="res://Scripts/CardGame.gd" id="1_script"]
[ext_resource type="Script" path="res://Scripts/HandManager.gd" id="2_hand"]
[ext_resource type="Script" path="res://Scripts/CardGameUI.gd" id="3_ui"]
[ext_resource type="Script" path="res://Scripts/MultiplayerManager.gd" id="4_multi"]
[ext_resource type="Script" path="res://Scripts/EffectManager.gd" id="5_effect"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(0.1, 0.2, 0.3, 0.9)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
corner_radius_top_left = 15
corner_radius_top_right = 15
corner_radius_bottom_right = 15
corner_radius_bottom_left = 15
shadow_color = Color(0, 0, 0, 0.3)
shadow_size = 5

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_2"]
bg_color = Color(0.2, 0.1, 0.3, 0.9)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
corner_radius_top_left = 15
corner_radius_top_right = 15
corner_radius_bottom_right = 15
corner_radius_bottom_left = 15
shadow_color = Color(0, 0, 0, 0.3)
shadow_size = 5

[node name="Root" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_script")

[node name="HandManager" type="Node" parent="."]
script = ExtResource("2_hand")

[node name="UIManager" type="Node" parent="."]
script = ExtResource("3_ui")

[node name="MultiplayerManager" type="Node" parent="."]
script = ExtResource("4_multi")

[node name="EffectManager" type="Node" parent="."]
script = ExtResource("5_effect")

[node name="MainLayout" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="OpponentHand" type="HBoxContainer" parent="."]
layout_mode = 2
offset_left = 141.0
offset_top = 22.0
offset_right = 1120.0
offset_bottom = 172.0
alignment = 1

[node name="MenuButton" type="Button" parent="."]
layout_mode = 0
offset_left = 20.0
offset_top = 20.0
offset_right = 120.0
offset_bottom = 60.0
text = "Menu"

[node name="ButtonsContainer" type="HBoxContainer" parent="."]
layout_mode = 2
offset_top = 481.0
offset_right = 1280.0
offset_bottom = 512.0
alignment = 1

[node name="BestHandButton" type="Button" parent="ButtonsContainer"]
layout_mode = 2
text = "Meilleure main"

[node name="CheatRankButton" type="Button" parent="ButtonsContainer"]
layout_mode = 2
text = "Changer rang"

[node name="CheatColorButton" type="Button" parent="ButtonsContainer"]
layout_mode = 2
text = "Changer couleur"

[node name="PlayHandButton" type="Button" parent="ButtonsContainer"]
layout_mode = 2
text = "Jouer la main"

[node name="Player1Zone" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.327
anchor_top = 0.319
anchor_right = 0.62
anchor_bottom = 0.658
offset_left = -276.56
offset_top = 0.319992
offset_right = -276.592
offset_bottom = -0.0320435
grow_horizontal = 2
grow_vertical = 2

[node name="Player1Label" type="Label" parent="Player1Zone"]
layout_mode = 2
text = "Joueur 1"
horizontal_alignment = 2

[node name="Player1Hand" type="GridContainer" parent="Player1Zone"]
layout_mode = 2
size_flags_vertical = 3
columns = 3

[node name="Player2Zone" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.625
anchor_top = 0.319
anchor_right = 0.928
anchor_bottom = 0.658
offset_left = -121.0
offset_top = 0.319992
offset_right = -121.272
offset_bottom = -0.0320435
grow_horizontal = 2
grow_vertical = 2

[node name="Player2Label" type="Label" parent="Player2Zone"]
layout_mode = 2
text = "Joueur 2"
horizontal_alignment = 2

[node name="Player2Hand" type="GridContainer" parent="Player2Zone"]
layout_mode = 2
size_flags_vertical = 3
columns = 3

[node name="WinnerScreen" type="Panel" parent="."]
visible = false
layout_mode = 0
offset_left = 156.0
offset_top = 62.0
offset_right = 1119.0
offset_bottom = 642.0

[node name="Label" type="Label" parent="WinnerScreen"]
layout_mode = 0
offset_left = 69.0
offset_top = 48.0
offset_right = 877.0
offset_bottom = 524.0
text = "GAGNANT : "
horizontal_alignment = 1
vertical_alignment = 1

[node name="ChangeRankPanel" type="Panel" parent="."]
visible = false
layout_mode = 0
offset_left = 390.0
offset_top = 150.0
offset_right = 890.0
offset_bottom = 500.0
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="Label" type="Label" parent="ChangeRankPanel"]
layout_mode = 0
offset_left = 10.0
offset_right = 490.0
offset_bottom = 50.0
theme_override_colors/font_color = Color(1, 0.9, 0.2, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 0.5)
theme_override_constants/shadow_offset_x = 2
theme_override_constants/shadow_offset_y = 2
theme_override_font_sizes/font_size = 24
text = "Modifiez le rang de votre carte"
horizontal_alignment = 1
vertical_alignment = 1
text_overrun_behavior = 3

[node name="ChangeColorPanel" type="Panel" parent="."]
visible = false
layout_mode = 0
offset_left = 390.0
offset_top = 150.0
offset_right = 890.0
offset_bottom = 500.0
theme_override_styles/panel = SubResource("StyleBoxFlat_2")

[node name="Label" type="Label" parent="ChangeColorPanel"]
layout_mode = 0
offset_left = 10.0
offset_right = 490.0
offset_bottom = 50.0
theme_override_colors/font_color = Color(1, 0.9, 0.2, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 0.5)
theme_override_constants/shadow_offset_x = 2
theme_override_constants/shadow_offset_y = 2
theme_override_font_sizes/font_size = 24
text = "Modifiez la couleur de votre carte"
horizontal_alignment = 1
vertical_alignment = 1
text_overrun_behavior = 3

[node name="CardHand" type="HBoxContainer" parent="."]
layout_mode = 2
offset_left = 169.0
offset_top = 547.0
offset_right = 1148.0
offset_bottom = 697.0
alignment = 1

[node name="Card1" type="TextureButton" parent="CardHand"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2

[node name="Card2" type="TextureButton" parent="CardHand"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2

[node name="Card3" type="TextureButton" parent="CardHand"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2

[node name="Card4" type="TextureButton" parent="CardHand"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2

[node name="Card5" type="TextureButton" parent="CardHand"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2

[node name="Card6" type="TextureButton" parent="CardHand"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2

[node name="Card7" type="TextureButton" parent="CardHand"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2

[node name="Card8" type="TextureButton" parent="CardHand"]
custom_minimum_size = Vector2(100, 150)
layout_mode = 2

[node name="HandValue" type="Label" parent="."]
layout_mode = 2
offset_top = 438.0
offset_right = 1280.0
offset_bottom = 461.0
text = "Sélectionnez des cartes"
horizontal_alignment = 1

[node name="Background" type="ColorRect" parent="."]
z_index = -1
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
color = Color(0.049134, 0.240927, 0.082257, 1)
